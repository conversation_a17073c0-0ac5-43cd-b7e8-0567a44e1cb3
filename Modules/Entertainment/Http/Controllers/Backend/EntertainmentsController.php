<?php

namespace Modules\Entertainment\Http\Controllers\Backend;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Modules\Entertainment\Models\Entertainment;
use Illuminate\Http\Request;
use Modules\Entertainment\Http\Requests\EntertainmentRequest;
use App\Trait\ModuleTrait;
use Modules\Constant\Models\Constant;
use Modules\Subscriptions\Models\Plan;
use Modules\Genres\Models\Genres;
use Modules\CastCrew\Models\CastCrew;
use Modules\Entertainment\Services\EntertainmentService;
use Modules\World\Models\Country;
use Illuminate\Support\Facades\Cache;
use Modules\Entertainment\Models\Source;

class EntertainmentsController extends Controller
{
    protected string $exportClass = '\App\Exports\EntertainmentExport';


    use ModuleTrait {
        initializeModuleTrait as private traitInitializeModuleTrait;
        }

        protected $entertainmentService;

        public function __construct(EntertainmentService $entertainmentService)
        {
            $this->entertainmentService = $entertainmentService;

            $this->traitInitializeModuleTrait(
                'entertainment.title',
                'entertainments',

                'fa-solid fa-clipboard-list'
            );
        }


    public function index(Request $request)
    {
        $filter = [
            'status' => $request->status,
        ];

        $module_action = 'List';

        $export_import = true;
        $export_columns = [
            [
                'value' => 'name',
                'text' => ' Name',
            ]
        ];
        $export_url = route('backend.entertainments.export');

        return view('entertainment::backend.entertainment.index', compact('module_action', 'filter', 'export_import', 'export_columns', 'export_url'));
    }

    public function bulk_action(Request $request)
    {
        $ids = explode(',', $request->rowIds);
        $actionType = $request->action_type;
        $moduleName = 'Entertainment'; // Adjust as necessary for dynamic use

        Cache::flush();


        return $this->performBulkAction(Entertainment::class, $ids, $actionType, $moduleName);
    }

    public function store(EntertainmentRequest $request)
     {

        $data = $request->all();
        $data['thumbnail_url'] = !empty($data['tmdb_id']) ? $data['thumbnail_url'] :extractFileNameFromUrl($data['thumbnail_url']);
        $data['poster_url']= !empty( $data['tmdb_id']) ?  $data['poster_url'] : extractFileNameFromUrl($data['poster_url']);


        if (isset($data['IMDb_rating'])) {
            // Round the IMDb rating to 1 decimal place
            $data['IMDb_rating'] = round($data['IMDb_rating'], 1);
        }

        if($request->trailer_url_type == 'Local'){
            $data['trailer_video'] = extractFileNameFromUrl($data['trailer_video']);
        }
        if($request->video_upload_type == 'Local'){
            $data['video_file_input'] = extractFileNameFromUrl($data['video_file_input']);
        }

        $entertainment = $this->entertainmentService->create($data);
        $type = $entertainment->type;
        $message = trans('messages.create_form', ['type' =>ucfirst($type)]);


        Cache::flush();

        if($type=='movie'){

            return redirect()->route('backend.movies.index')->with('success', $message);

        }else{

            return redirect()->route('backend.tvshows.index')->with('success', $message);
        }
    }

    public function update_status(Request $request, Entertainment $id)
    {
        $id->update(['status' => $request->status]);

        Cache::flush();

        return response()->json(['status' => true, 'message' => __('messages.status_updated')]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id)
    {

        $data = Entertainment::where('id', $id)
            ->with([
                'entertainmentGenerMappings',
                'entertainmentCountryMappings',
                'entertainmentStreamContentMappings',
                'entertainmentTalentMappings'
            ])
            ->first();

        $tmdb_id = $data->tmdb_id;
        $data->thumbnail_url = setBaseUrlWithFileName($data->thumbnail_url);
        $data->poster_url =setBaseUrlWithFileName($data->poster_url);

        if($data->trailer_url_type =='Local'){

            $data->trailer_url = setBaseUrlWithFileName($data->trailer_url);
        }

        if($data->video_upload_type =='Local'){

            $data->video_url_input = setBaseUrlWithFileName($data->video_url_input);
        }


        $constants = Constant::whereIn('type', ['upload_type', 'movie_language', 'video_quality', 'source_type'])->get();
        $upload_url_type = $constants->where('type', 'upload_type');
        $movie_language = $constants->where('type', 'movie_language');
        $video_quality = $constants->where('type', 'video_quality');
        $source_type = $constants->where('type', 'source_type');

        $plan = Plan::where('status', 1)->get();
        $genres = Genres::where('status', 1)->get();
        $actors = CastCrew::where('type', 'actor')->get();
        $directors = CastCrew::where('type', 'director')->get();
        $countries = Country::where('status', 1)->get();
        $mediaUrls = getMediaUrls();
        $assets = ['textarea'];

        if ($data->type === 'tvshow') {
            $module_title = __('tvshow.edit_title');
        } else {
            $module_title = __('movie.edit_title');
        }


        $numberOptions = collect(range(1, 10))->mapWithKeys(function ($number) {
            return [$number => $number];
        });

        $data['genres'] = $data->entertainmentGenerMappings->pluck('genre_id')->toArray();
        $data['countries'] = $data->entertainmentCountryMappings->pluck('country_id')->toArray();
        $data['actors'] = $data->entertainmentTalentMappings->pluck('talent_id')->toArray();
        $data['directors'] = $data->entertainmentTalentMappings->pluck('talent_id')->toArray();

        $data['sources'] = $data->sources;
        $data['source_type'] = $source_type;
        return view('entertainment::backend.entertainment.edit', compact(
            'data',
            'tmdb_id',
            'upload_url_type',
            'plan',
            'movie_language',
            'genres',
            'numberOptions',
            'actors',
            'directors',
            'countries',
            'video_quality',
            'source_type',
            'mediaUrls',
            'assets',
            'module_title'

        ));
    }


    public function update(EntertainmentRequest $request, $id)
    {
        // Log pour déboguer les données reçues
        \Log::info('Update Entertainment - Données brutes:', [
            'request_all' => $request->all(),
            'request_input' => [
                'is_iptv' => $request->input('is_iptv'),
                'status' => $request->input('status'),
                'name' => $request->input('name'),
                'description' => $request->input('description'),
                'movie_access' => $request->input('movie_access'),
                'genres' => $request->input('genres'),
                'actors' => $request->input('actors'),
                'directors' => $request->input('directors')
            ]
        ]);

        Cache::flush();
        $request_data = $request->all();
        
        // Gérer les champs checkbox qui peuvent être absents
        if (!isset($request_data['is_iptv'])) {
            $request_data['is_iptv'] = 0;
            \Log::info('is_iptv non défini, mis à 0');
        }
        if (!isset($request_data['status'])) {
            $request_data['status'] = 0;
            \Log::info('status non défini, mis à 0');
        }

        // Log pour déboguer les données après traitement
        \Log::info('Update Entertainment - Données après traitement:', [
            'id' => $id,
            'is_iptv' => $request_data['is_iptv'],
            'status' => $request_data['status']
        ]);
        
        $request_data['thumbnail_url'] = !empty($request_data['tmdb_id']) ? $request_data['thumbnail_url'] :extractFileNameFromUrl($request_data['thumbnail_url']);
        $request_data['poster_url'] = !empty($request_data['tmdb_id']) ? $request_data['poster_url'] : extractFileNameFromUrl($request_data['poster_url']);
        $request_data['trailer_video'] = extractFileNameFromUrl($request_data['trailer_video']);
        $request_data['video_file_input'] = isset($request_data['video_file_input'])  ? extractFileNameFromUrl($request_data['video_file_input']) : null;

        if (isset($request_data['IMDb_rating'])) {
            // Round the IMDb rating to 1 decimal place
            $request_data['IMDb_rating'] = round($request_data['IMDb_rating'], 1);
        }

        $entertainment = $this->entertainmentService->getById($id);

        // Handle Poster Image
        if ($request->input('remove_image') == 1) {
            $requestData['poster_url'] = setDefaultImage($request_data['poster_url']);


        } elseif ($request->hasFile('poster_url')) {
            $file = $request->file('poster_url');
            StoreMediaFile($entertainment, $file, 'poster_url');
            $requestData['poster_url'] = $entertainment->getFirstMediaUrl('poster_url');
        } else {
            $requestData['poster_url'] = $entertainment->poster_url;
        }

        // Handle Thumbnail Image
        if ($request->input('remove_image_thumbnail') == 1) {
            $requestData['thumbnail_url'] = setDefaultImage($request_data['thumbnail_url']);
        } elseif ($request->hasFile('thumbnail_url')) {
            $file = $request->file('thumbnail_url');
            StoreMediaFile($entertainment, $file, 'thumbnail_url');
            $requestData['thumbnail_url'] = $entertainment->getFirstMediaUrl('thumbnail_url');
        } else {
            $requestData['thumbnail_url'] = $entertainment->thumbnail_url;
        }
        
        // Log avant la mise à jour
        \Log::info('Update Entertainment - Données finales avant update:', [
            'is_iptv' => $request_data['is_iptv'],
            'status' => $request_data['status']
        ]);
        
        $data = $this->entertainmentService->update($id, $request_data);

        Cache::flush();


        $type = $entertainment->type;
        $message = trans('messages.update_form', ['Form' =>ucfirst($type)]);

        if ($type == 'movie') {
            return redirect()->route('backend.movies.index')
                ->with('success', $message);
        } else if ($type == 'tvshow') {
            return redirect()->route('backend.tvshows.index')
                ->with('success', $message);
        }
    }


    public function destroy($id)
    {
       $entertainment = $this->entertainmentService->getById($id);
       $type=$entertainment->type;
       $entertainment->delete();
       $message = trans('messages.delete_form', ['form' => $type]);
       Cache::flush();
       return response()->json(['message' => $message, 'status' => true], 200);
    }

    public function restore($id)
    {
        $entertainment = $this->entertainmentService->getById($id);
        $type=$entertainment->type;
        $entertainment->restore();
        $message = trans('messages.restore_form', ['form' =>$type]);
        Cache::flush();
        return response()->json(['message' => $message, 'status' => true], 200);

    }

    public function forceDelete($id)
    {
        $entertainment = $this->entertainmentService->getById($id);
        $type=$entertainment->type;
        $entertainment->forceDelete();
        $message = trans('messages.permanent_delete_form', ['form' =>$type]);
        Cache::flush();
        return response()->json(['message' => $message, 'status' => true], 200);
    }

    public function downloadOption(Request $request, $id){

        $data = Entertainment::where('id',$id)->with('entertainmentDownloadMappings')->first();

        $module_title =__('messages.download_movie');

        $upload_url_type=Constant::where('type','upload_type')->get();
        $video_quality=Constant::where('type','video_quality')->get();
        Cache::flush();

        return view('entertainment::backend.entertainment.download', compact('data','module_title','upload_url_type','video_quality'));

    }


   public function storeDownloads(Request $request, $id)
    {
        $data = $request->all();
        $this->entertainmentService->storeDownloads($data, $id);
        $message = trans('messages.set_download_url');
        Cache::flush();

        return redirect()->route('backend.movies.index')->with('success', $message);
    }


    public function details($id)
    {
        $data = Entertainment::with([
            'entertainmentGenerMappings',
            'entertainmentStreamContentMappings',
            'entertainmentTalentMappings',
            'entertainmentReviews',
            'season',

        ])->findOrFail($id);


       foreach ($data->entertainmentTalentMappings as $talentMapping) {
    $talentProfile = $talentMapping->talentprofile;

    if ($talentProfile) {
        if (in_array($talentProfile->type, ['actor', 'director'])) {
            $talentProfile->file_url =  setBaseUrlWithFileName($talentProfile->file_url);
        }
    }
}
        $data->poster_url =setBaseUrlWithFileName($data->poster_url);

        $data->formatted_release_date = Carbon::parse($data->release_date)->format('d M, Y');
        if($data->type == "movie"){
            $module_title = __('movie.title');
            $show_name = $data->name;
            $route = 'backend.movies.index';
        }else{
            $module_title = __('tvshow.title');
            $show_name = $data->name;
            $route = 'backend.tvshows.index';
        }

        return view('entertainment::backend.entertainment.details', compact('data','module_title','show_name','route'));
    }

    public function sendNotification(Request $request, $id)
    {
        $entertainment = Entertainment::findOrFail($id);
        
        // Construire le titre et le message de la notification
        $title = $entertainment->name;
        $message = \Str::limit($entertainment->description, 150) ?: 'Nouveau contenu disponible !';
        
        // Déterminer le type (movie ou tvshow)
        $type = $entertainment->type;
        
        // Construire les données de notification
        $notificationData = [
            'title' => $title,
            'body' => $message,
        ];

        // Ajouter l'image du poster si disponible
        if ($entertainment->poster_url) {
            $notificationData['image'] = setBaseUrlWithFileName($entertainment->poster_url);
        }

        // Construire les données personnalisées
        $customData = [
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
            'type' => $type,
            'movie_id' => (string)$entertainment->id,
            'entertainment_id' => (string)$entertainment->id
        ];

        // Envoyer la notification FCM avec la structure corrigée
        $fcmResult = fcm([
            'message' => [
                'topic' => 'voir_films_hd',
                'notification' => $notificationData,
                'data' => $customData,
                'android' => [
                    'notification' => [
                        'sound' => 'default',
                        'icon' => 'ic_notification',
                        'color' => '#FF6B35'
                    ]
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                            'badge' => 1
                        ]
                    ]
                ]
            ]
        ]);

        if ($fcmResult) {
            $message = "Notification envoyée avec succès pour \"{$entertainment->name}\" à tous les utilisateurs !";
            return response()->json(['status' => true, 'message' => $message]);
        } else {
            return response()->json(['status' => false, 'message' => 'Erreur lors de l\'envoi de la notification']);
        }
    }

    public function sendCustomNotification(Request $request, $id)
    {
        // Nettoyer les données avant validation
        if ($request->recipient_type !== 'token') {
            $request->request->remove('test_token');
        }
        
        $rules = [
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'large_icon' => 'nullable|url',
            'big_image' => 'nullable|url',
            'recipient_type' => 'required|in:all,token',
        ];
        
        // Ajouter la validation du test_token seulement si nécessaire
        if ($request->recipient_type === 'token') {
            $rules['test_token'] = 'required|string';
        }
        
        $request->validate($rules);

        $entertainment = Entertainment::findOrFail($id);
        
        // Construire les données de notification
        $notificationData = [
            'title' => $request->title,
            'body' => $request->body,
        ];

        // Construire les données personnalisées
        $customData = [
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
            'type' => $entertainment->type,
            'movie_id' => (string)$entertainment->id,
            'entertainment_id' => (string)$entertainment->id
        ];

        // Construire la structure FCM selon le type de destinataire
        if ($request->recipient_type === 'token') {
            // Envoi vers un token spécifique pour test
            $fcmData = [
                'message' => [
                    'token' => $request->test_token,
                    'notification' => $notificationData,
                    'data' => $customData,
                    'android' => [
                        'notification' => [
                            'sound' => 'default',
                            'icon' => 'ic_notification',
                            'color' => '#FF6B35'
                        ]
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'sound' => 'default',
                                'badge' => 1
                            ]
                        ]
                    ]
                ]
            ];
            $recipientText = "votre appareil de test";
        } else {
            // Envoi vers tous les utilisateurs (topic)
            $fcmData = [
                'message' => [
                    'topic' => 'voir_films_hd',
                    'notification' => $notificationData,
                    'data' => $customData,
                    'android' => [
                        'notification' => [
                            'sound' => 'default',
                            'icon' => 'ic_notification',
                            'color' => '#FF6B35'
                        ]
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'sound' => 'default',
                                'badge' => 1
                            ]
                        ]
                    ]
                ]
            ];
            $recipientText = "tous les utilisateurs";
        }

        // Ajouter l'image si fournie et valide
        if ($request->big_image) {
            // Vérifier que l'URL de l'image est accessible
            $imageUrl = $request->big_image;
            $headers = @get_headers($imageUrl);
            $imageValid = $headers && strpos($headers[0], '200') !== false;
            
            if ($imageValid) {
                $fcmData['message']['android']['notification']['image'] = $imageUrl;
                \Log::info('FCM Image URL validated: ' . $imageUrl);
            } else {
                \Log::warning('FCM Image URL not accessible: ' . $imageUrl);
            }
        }

        // Envoyer la notification FCM
        \Log::info('Sending FCM notification for entertainment: ' . $entertainment->name);
        \Log::info('FCM Data: ' . json_encode($fcmData));
        
        $fcmResult = fcm($fcmData);

        if ($fcmResult) {
            $message = "Notification personnalisée envoyée avec succès pour \"{$entertainment->name}\" vers {$recipientText} !";
            \Log::info('FCM Success: ' . $message);
            return response()->json(['status' => true, 'message' => $message]);
        } else {
            $errorMessage = 'Erreur lors de l\'envoi de la notification - Vérifiez les logs pour plus de détails';
            \Log::error('FCM Failed for entertainment: ' . $entertainment->name);
            return response()->json(['status' => false, 'message' => $errorMessage]);
        }
    }

    public function notificationPage($id)
    {
        $entertainment = Entertainment::findOrFail($id);
        
        $module_title = 'Notification personnalisée';
        $module_action = 'Envoyer une notification';
        
        return view('entertainment::backend.entertainment.notification', compact('entertainment', 'module_title', 'module_action'));
    }

}
